from src.evaluation.evaluation import calculate_results, calculate_results_diff_analysis
from src.evaluation.bell_test import I_3
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os

# Use a different subportion of data (middle third instead of first tenth)
data = pd.read_csv("data/hww_1M_MG_final_truth.csv")
length = len(data)
# Use middle third of the data (different from the first 1/10th used previously)
start_idx = length // 3
end_idx = 2 * length // 3
data = data[start_idx:end_idx]

X = data[
    [
        "p_l_1_E_truth",
        "p_l_1_x_truth",
        "p_l_1_y_truth",
        "p_l_1_z_truth",
        "p_l_2_E_truth",
        "p_l_2_x_truth",
        "p_l_2_y_truth",
        "p_l_2_z_truth",
    ]
].copy()
y = data[
    [
        "p_v_1_E_truth",
        "p_v_1_x_truth",
        "p_v_1_y_truth",
        "p_v_1_z_truth",
        "p_v_2_E_truth",
        "p_v_2_x_truth",
        "p_v_2_y_truth",
        "p_v_2_z_truth",
    ]
].copy()
types = data["Event.Type"].copy()

final_state = np.concatenate((X, y), axis=1)

data_cuts = pd.read_csv("data/hww_1M_MG_final_truth_cuts.csv")
length_cuts = len(data_cuts)
# Use same middle third portion for cuts data
start_idx_cuts = length_cuts // 3
end_idx_cuts = 2 * length_cuts // 3
data_cuts = data_cuts[start_idx_cuts:end_idx_cuts]

X_cuts = data_cuts[
    [
        "p_l_1_E_truth",
        "p_l_1_x_truth",
        "p_l_1_y_truth",
        "p_l_1_z_truth",
        "p_l_2_E_truth",
        "p_l_2_x_truth",
        "p_l_2_y_truth",
        "p_l_2_z_truth",
    ]
].copy()
y_cuts = data_cuts[
    [
        "p_v_1_E_truth",
        "p_v_1_x_truth",
        "p_v_1_y_truth",
        "p_v_1_z_truth",
        "p_v_2_E_truth",
        "p_v_2_x_truth",
        "p_v_2_y_truth",
        "p_v_2_z_truth",
    ]
].copy()
types_cuts = data_cuts["Event.Type"].copy()

final_state_cuts = np.concatenate((X_cuts, y_cuts), axis=1)


def compute_efficiency_map(truth_data, cuts_data, truth_types, cuts_types, target_path):
    """
    Compute efficiency map using angular distributions from evaluation.py functions.
    """
    os.makedirs(target_path, exist_ok=True)

    # Create a temporary calculate_results object to access the angular computation methods
    temp_result = calculate_results(
        arrays=[truth_data, cuts_data],
        labels=["Truth", "Cuts"],
        title="Efficiency Map Computation",
        types=[truth_types, cuts_types],
    )

    # Initialize datasets to get angular distributions
    temp_result.initialize_datasets()

    # Extract angular data (phi and cos_theta for both W+ and W-)
    N_cos = 12
    N_phi = 20
    cos_edges = np.linspace(-1, 1, N_cos + 1)
    phi_edges = np.linspace(-np.pi, np.pi, N_phi + 1)

    # Create 2D histograms for truth and cuts data
    H_truth_Wminus, *_ = np.histogram2d(
        temp_result.angles_[0][:, 2],
        temp_result.angles_[0][:, 0],
        bins=[cos_edges, phi_edges],
    )
    H_truth_Wplus, *_ = np.histogram2d(
        temp_result.angles_[0][:, 3],
        temp_result.angles_[0][:, 1],
        bins=[cos_edges, phi_edges],
    )
    H_cuts_Wminus, *_ = np.histogram2d(
        temp_result.angles_[1][:, 2],
        temp_result.angles_[1][:, 0],
        bins=[cos_edges, phi_edges],
    )
    H_cuts_Wplus, *_ = np.histogram2d(
        temp_result.angles_[1][:, 3],
        temp_result.angles_[1][:, 1],
        bins=[cos_edges, phi_edges],
    )

    # Compute efficiency maps (avoid division by zero)
    efficiency_Wminus = np.divide(
        H_cuts_Wminus,
        H_truth_Wminus,
        out=np.zeros_like(H_cuts_Wminus),
        where=H_truth_Wminus != 0,
    )
    efficiency_Wplus = np.divide(
        H_cuts_Wplus,
        H_truth_Wplus,
        out=np.zeros_like(H_cuts_Wplus),
        where=H_truth_Wplus != 0,
    )

    # Save efficiency maps
    np.save(os.path.join(target_path, "efficiency_map_Wminus.npy"), efficiency_Wminus)
    np.save(os.path.join(target_path, "efficiency_map_Wplus.npy"), efficiency_Wplus)

    # Plot efficiency maps
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    im1 = ax1.imshow(
        efficiency_Wminus.T,
        origin="lower",
        aspect="auto",
        extent=[-1, 1, -np.pi, np.pi],
        vmin=0,
        vmax=1,
    )
    ax1.set_title("Efficiency Map W⁻")
    ax1.set_xlabel("cos θ")
    ax1.set_ylabel("φ")
    plt.colorbar(im1, ax=ax1)

    im2 = ax2.imshow(
        efficiency_Wplus.T,
        origin="lower",
        aspect="auto",
        extent=[-1, 1, -np.pi, np.pi],
        vmin=0,
        vmax=1,
    )
    ax2.set_title("Efficiency Map W⁺")
    ax2.set_xlabel("cos θ")
    ax2.set_ylabel("φ")
    plt.colorbar(im2, ax=ax2)

    plt.tight_layout()
    plt.savefig(os.path.join(target_path, "efficiency_maps.png"), dpi=150)
    plt.close()

    print(f"Efficiency maps saved to {target_path}")
    return efficiency_Wminus, efficiency_Wplus, cos_edges, phi_edges


def compute_weighted_gellmann_matrix(
    cuts_data,
    cuts_types,
    efficiency_Wminus,
    efficiency_Wplus,
    cos_edges,
    phi_edges,
    target_path,
):
    """
    Compute weighted Gell-Mann matrix using static class methods from evaluation.py.
    Events with cuts applied are reweighted with the efficiency map.
    """
    os.makedirs(target_path, exist_ok=True)

    # Create calculate_results object for cuts data to get angular distributions
    cuts_result = calculate_results(
        arrays=[cuts_data],
        labels=["Cuts"],
        title="Weighted Gell-Mann Matrix",
        types=[cuts_types],
    )
    cuts_result.initialize_datasets()

    # Get angular data for cuts events
    angles_cuts = cuts_result.angles_[
        0
    ]  # [phi_Wminus, phi_Wplus, cos_theta_Wminus, cos_theta_Wplus]

    # Calculate weights for each event based on efficiency maps
    weights = []
    for i in range(len(angles_cuts)):
        phi_Wminus = angles_cuts[i, 0]
        phi_Wplus = angles_cuts[i, 1]
        cos_theta_Wminus = angles_cuts[i, 2]
        cos_theta_Wplus = angles_cuts[i, 3]

        # Find bin indices for efficiency lookup
        cos_bin_Wminus = np.digitize(cos_theta_Wminus, cos_edges) - 1
        phi_bin_Wminus = np.digitize(phi_Wminus, phi_edges) - 1
        cos_bin_Wplus = np.digitize(cos_theta_Wplus, cos_edges) - 1
        phi_bin_Wplus = np.digitize(phi_Wplus, phi_edges) - 1

        # Ensure indices are within bounds
        cos_bin_Wminus = np.clip(cos_bin_Wminus, 0, len(cos_edges) - 2)
        phi_bin_Wminus = np.clip(phi_bin_Wminus, 0, len(phi_edges) - 2)
        cos_bin_Wplus = np.clip(cos_bin_Wplus, 0, len(cos_edges) - 2)
        phi_bin_Wplus = np.clip(phi_bin_Wplus, 0, len(phi_edges) - 2)

        # Get efficiency values (inverse weighting: 1/efficiency to correct for selection bias)
        eff_Wminus = efficiency_Wminus[cos_bin_Wminus, phi_bin_Wminus]
        eff_Wplus = efficiency_Wplus[cos_bin_Wplus, phi_bin_Wplus]

        # Combined weight (avoid division by zero)
        weight = 1.0
        if eff_Wminus > 0:
            weight *= 1.0 / eff_Wminus
        if eff_Wplus > 0:
            weight *= 1.0 / eff_Wplus

        weights.append(weight)

    weights = np.array(weights)

    # Normalize weights to avoid extreme values
    weights = np.clip(weights, 0, np.percentile(weights, 95))  # Cap at 95th percentile
    weights = weights / np.mean(weights)  # Normalize to mean = 1

    print(
        f"Weight statistics: min={np.min(weights):.3f}, max={np.max(weights):.3f}, mean={np.mean(weights):.3f}"
    )

    # Compute weighted Gell-Mann coefficients using static methods
    pW1_weighted = np.zeros(8)
    pW2_weighted = np.zeros(8)
    cov_weighted = np.zeros((8, 8))

    # Get Gell-Mann coefficients for cuts data
    pW1_cuts = cuts_result.pW1[0]  # Shape: (n_events, 8)
    pW2_cuts = cuts_result.pW2[0]  # Shape: (n_events, 8)
    cov_cuts = cuts_result.datasets[0]  # Shape: (n_events, 8, 8)

    # Apply weights to compute weighted averages
    total_weight = np.sum(weights)
    for i in range(len(weights)):
        weight = weights[i] / total_weight
        pW1_weighted += weight * pW1_cuts[i]
        pW2_weighted += weight * pW2_cuts[i]
        cov_weighted += weight * cov_cuts[i]

    # Create weighted density matrix using static methods
    weighted_density_matrix = np.zeros((9, 9))

    # Fill covariance terms (indices 1-8, 1-8)
    for k in range(1, 9):
        for m in range(1, 9):
            weighted_density_matrix[k, m] = cov_weighted[k - 1, m - 1] / 4.0

    # Fill pW terms
    for k in range(1, 9):
        weighted_density_matrix[0, k] = pW2_weighted[k - 1] / 2.0
        weighted_density_matrix[k, 0] = pW1_weighted[k - 1] / 2.0

    # Calculate Bell inequality using static method
    bell_value_weighted = I_3.CGLMP_test(weighted_density_matrix)

    # Save results
    np.save(
        os.path.join(target_path, "weighted_density_matrix.npy"),
        weighted_density_matrix,
    )
    np.save(os.path.join(target_path, "weights.npy"), weights)
    np.save(os.path.join(target_path, "pW1_weighted.npy"), pW1_weighted)
    np.save(os.path.join(target_path, "pW2_weighted.npy"), pW2_weighted)
    np.save(os.path.join(target_path, "cov_weighted.npy"), cov_weighted)

    print(f"Weighted Bell inequality value: {bell_value_weighted:.6f}")
    print(f"Weighted Gell-Mann matrix results saved to {target_path}")

    return weighted_density_matrix, bell_value_weighted, weights


# Main execution
if __name__ == "__main__":
    print("Starting angular distribution analysis with efficiency map computation...")
    print(f"Using data from index {start_idx} to {end_idx} (middle third of dataset)")
    print(f"Truth data shape: {final_state.shape}")
    print(f"Cuts data shape: {final_state_cuts.shape}")

    # Step 1: Run original analysis for comparison
    print("\n=== Step 1: Running original analysis ===")
    result = calculate_results(
        arrays=[final_state, final_state_cuts],
        labels=["Truth", "Cuts"],
        title="Cuts Analysis HWW - Middle Third",
        types=[types, types_cuts],
    )
    result.run("reports/")

    diff_result = calculate_results_diff_analysis(
        arrays=[final_state, final_state_cuts],
        labels=["Truth", "Cuts"],
        title="Diff Analysis HWW - Middle Third",
        types=[types, types_cuts],
    )
    diff_result.run("reports/")

    # Step 2: Compute efficiency map
    print("\n=== Step 2: Computing efficiency map ===")
    efficiency_Wminus, efficiency_Wplus, cos_edges, phi_edges = compute_efficiency_map(
        final_state, final_state_cuts, types, types_cuts, "reports/efficiency_maps/"
    )

    # Step 3: Compute weighted Gell-Mann matrix
    print("\n=== Step 3: Computing weighted Gell-Mann matrix ===")
    weighted_density_matrix, bell_value_weighted, weights = (
        compute_weighted_gellmann_matrix(
            final_state_cuts,
            types_cuts,
            efficiency_Wminus,
            efficiency_Wplus,
            cos_edges,
            phi_edges,
            "reports/weighted_analysis/",
        )
    )

    # Step 4: Compare results
    print("\n=== Step 4: Results Summary ===")

    # Get unweighted Bell value for comparison
    unweighted_result = calculate_results_diff_analysis(
        arrays=[final_state_cuts],
        labels=["Cuts_Unweighted"],
        title="Unweighted Analysis",
        types=[types_cuts],
    )
    unweighted_result.initialize_datasets()
    bell_value_unweighted = unweighted_result.bell_inequality_averaged(
        "reports/comparison/"
    )

    print(f"Unweighted Bell inequality (cuts only): {bell_value_unweighted[0]:.6f}")
    print(f"Weighted Bell inequality (efficiency corrected): {bell_value_weighted:.6f}")
    print(f"Difference: {bell_value_weighted - bell_value_unweighted[0]:.6f}")

    # Save comparison results
    comparison_results = {
        "unweighted_bell": bell_value_unweighted[0],
        "weighted_bell": bell_value_weighted,
        "difference": bell_value_weighted - bell_value_unweighted[0],
        "weight_stats": {
            "min": float(np.min(weights)),
            "max": float(np.max(weights)),
            "mean": float(np.mean(weights)),
            "std": float(np.std(weights)),
        },
    }

    import json

    os.makedirs("reports/comparison/", exist_ok=True)
    with open("reports/comparison/bell_comparison.json", "w") as f:
        json.dump(comparison_results, f, indent=2)

    print(f"\nAnalysis complete! Results saved in reports/ directory.")
    print(f"Efficiency maps: reports/efficiency_maps/")
    print(f"Weighted analysis: reports/weighted_analysis/")
    print(f"Comparison: reports/comparison/")
