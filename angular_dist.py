from src.evaluation.evaluation import calculate_results, calculate_results_diff_analysis
from src.evaluation.bell_test import I_3
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os

# Use smaller subset (10%) and split for realistic measurement simulation
data = pd.read_csv("data/hww_1M_MG_final_truth.csv")
length = len(data)
# Use first 10% of data and split it: first half for efficiency map, second half for measurement
subset_size = length // 10
efficiency_map_data = data[: subset_size // 2]  # First half for efficiency map
measurement_data = data[subset_size // 2 : subset_size]  # Second half for measurement

# Process efficiency map data (first half)
X_eff = efficiency_map_data[
    [
        "p_l_1_E_truth",
        "p_l_1_x_truth",
        "p_l_1_y_truth",
        "p_l_1_z_truth",
        "p_l_2_E_truth",
        "p_l_2_x_truth",
        "p_l_2_y_truth",
        "p_l_2_z_truth",
    ]
].copy()
y_eff = efficiency_map_data[
    [
        "p_v_1_E_truth",
        "p_v_1_x_truth",
        "p_v_1_y_truth",
        "p_v_1_z_truth",
        "p_v_2_E_truth",
        "p_v_2_x_truth",
        "p_v_2_y_truth",
        "p_v_2_z_truth",
    ]
].copy()
types_eff = efficiency_map_data["Event.Type"].copy()
final_state_eff = np.concatenate((X_eff, y_eff), axis=1)

# Process measurement data (second half)
X_meas = measurement_data[
    [
        "p_l_1_E_truth",
        "p_l_1_x_truth",
        "p_l_1_y_truth",
        "p_l_1_z_truth",
        "p_l_2_E_truth",
        "p_l_2_x_truth",
        "p_l_2_y_truth",
        "p_l_2_z_truth",
    ]
].copy()
y_meas = measurement_data[
    [
        "p_v_1_E_truth",
        "p_v_1_x_truth",
        "p_v_1_y_truth",
        "p_v_1_z_truth",
        "p_v_2_E_truth",
        "p_v_2_x_truth",
        "p_v_2_y_truth",
        "p_v_2_z_truth",
    ]
].copy()
types_meas = measurement_data["Event.Type"].copy()
final_state_meas = np.concatenate((X_meas, y_meas), axis=1)

# Load cuts data and split similarly
data_cuts = pd.read_csv("data/hww_1M_MG_final_truth_cuts.csv")
length_cuts = len(data_cuts)
subset_size_cuts = length_cuts // 10
efficiency_map_cuts = data_cuts[: subset_size_cuts // 2]
measurement_cuts = data_cuts[subset_size_cuts // 2 : subset_size_cuts]

# Process cuts data for efficiency map
X_cuts_eff = efficiency_map_cuts[
    [
        "p_l_1_E_truth",
        "p_l_1_x_truth",
        "p_l_1_y_truth",
        "p_l_1_z_truth",
        "p_l_2_E_truth",
        "p_l_2_x_truth",
        "p_l_2_y_truth",
        "p_l_2_z_truth",
    ]
].copy()
y_cuts_eff = efficiency_map_cuts[
    [
        "p_v_1_E_truth",
        "p_v_1_x_truth",
        "p_v_1_y_truth",
        "p_v_1_z_truth",
        "p_v_2_E_truth",
        "p_v_2_x_truth",
        "p_v_2_y_truth",
        "p_v_2_z_truth",
    ]
].copy()
types_cuts_eff = efficiency_map_cuts["Event.Type"].copy()
final_state_cuts_eff = np.concatenate((X_cuts_eff, y_cuts_eff), axis=1)

# Process cuts data for measurement
X_cuts_meas = measurement_cuts[
    [
        "p_l_1_E_truth",
        "p_l_1_x_truth",
        "p_l_1_y_truth",
        "p_l_1_z_truth",
        "p_l_2_E_truth",
        "p_l_2_x_truth",
        "p_l_2_y_truth",
        "p_l_2_z_truth",
    ]
].copy()
y_cuts_meas = measurement_cuts[
    [
        "p_v_1_E_truth",
        "p_v_1_x_truth",
        "p_v_1_y_truth",
        "p_v_1_z_truth",
        "p_v_2_E_truth",
        "p_v_2_x_truth",
        "p_v_2_y_truth",
        "p_v_2_z_truth",
    ]
].copy()
types_cuts_meas = measurement_cuts["Event.Type"].copy()
final_state_cuts_meas = np.concatenate((X_cuts_meas, y_cuts_meas), axis=1)


def compute_efficiency_map(truth_data, cuts_data, truth_types, cuts_types, target_path):
    """
    Compute efficiency map using angular distributions from evaluation.py functions.
    """
    os.makedirs(target_path, exist_ok=True)

    # Create a temporary calculate_results object to access the angular computation methods
    temp_result = calculate_results(
        arrays=[truth_data, cuts_data],
        labels=["Truth", "Cuts"],
        title="Efficiency Map Computation",
        types=[truth_types, cuts_types],
    )

    # Initialize datasets to get angular distributions
    temp_result.initialize_datasets()

    # Extract angular data (phi and cos_theta for both W+ and W-)
    N_cos = 12
    N_phi = 20
    cos_edges = np.linspace(-1, 1, N_cos + 1)
    phi_edges = np.linspace(-np.pi, np.pi, N_phi + 1)

    # Create 2D histograms for truth and cuts data
    H_truth_Wminus, *_ = np.histogram2d(
        temp_result.angles_[0][:, 2],
        temp_result.angles_[0][:, 0],
        bins=[cos_edges, phi_edges],
    )
    H_truth_Wplus, *_ = np.histogram2d(
        temp_result.angles_[0][:, 3],
        temp_result.angles_[0][:, 1],
        bins=[cos_edges, phi_edges],
    )
    H_cuts_Wminus, *_ = np.histogram2d(
        temp_result.angles_[1][:, 2],
        temp_result.angles_[1][:, 0],
        bins=[cos_edges, phi_edges],
    )
    H_cuts_Wplus, *_ = np.histogram2d(
        temp_result.angles_[1][:, 3],
        temp_result.angles_[1][:, 1],
        bins=[cos_edges, phi_edges],
    )

    # Compute efficiency maps (avoid division by zero)
    efficiency_Wminus = np.divide(
        H_cuts_Wminus,
        H_truth_Wminus,
        out=np.zeros_like(H_cuts_Wminus),
        where=H_truth_Wminus != 0,
    )
    efficiency_Wplus = np.divide(
        H_cuts_Wplus,
        H_truth_Wplus,
        out=np.zeros_like(H_cuts_Wplus),
        where=H_truth_Wplus != 0,
    )

    # Save efficiency maps
    np.save(os.path.join(target_path, "efficiency_map_Wminus.npy"), efficiency_Wminus)
    np.save(os.path.join(target_path, "efficiency_map_Wplus.npy"), efficiency_Wplus)

    # Plot efficiency maps
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    im1 = ax1.imshow(
        efficiency_Wminus.T,
        origin="lower",
        aspect="auto",
        extent=[-1, 1, -np.pi, np.pi],
        vmin=0,
        vmax=1,
    )
    ax1.set_title("Efficiency Map W⁻")
    ax1.set_xlabel("cos θ")
    ax1.set_ylabel("φ")
    plt.colorbar(im1, ax=ax1)

    im2 = ax2.imshow(
        efficiency_Wplus.T,
        origin="lower",
        aspect="auto",
        extent=[-1, 1, -np.pi, np.pi],
        vmin=0,
        vmax=1,
    )
    ax2.set_title("Efficiency Map W⁺")
    ax2.set_xlabel("cos θ")
    ax2.set_ylabel("φ")
    plt.colorbar(im2, ax=ax2)

    plt.tight_layout()
    plt.savefig(os.path.join(target_path, "efficiency_maps.png"), dpi=150)
    plt.close()

    print(f"Efficiency maps saved to {target_path}")
    return efficiency_Wminus, efficiency_Wplus, cos_edges, phi_edges


def compute_weighted_gellmann_matrix(
    cuts_data,
    cuts_types,
    efficiency_Wminus,
    efficiency_Wplus,
    cos_edges,
    phi_edges,
    target_path,
):
    """
    Compute weighted Gell-Mann matrix using static class methods from evaluation.py.
    Events with cuts applied are reweighted with the efficiency map.
    """
    os.makedirs(target_path, exist_ok=True)

    # Create calculate_results object for cuts data to get angular distributions
    cuts_result = calculate_results(
        arrays=[cuts_data],
        labels=["Cuts"],
        title="Weighted Gell-Mann Matrix",
        types=[cuts_types],
    )
    cuts_result.initialize_datasets()

    # Get angular data for cuts events
    angles_cuts = cuts_result.angles_[
        0
    ]  # [phi_Wminus, phi_Wplus, cos_theta_Wminus, cos_theta_Wplus]

    # Calculate weights for each event based on efficiency maps
    weights = []
    for i in range(len(angles_cuts)):
        phi_Wminus = angles_cuts[i, 0]
        phi_Wplus = angles_cuts[i, 1]
        cos_theta_Wminus = angles_cuts[i, 2]
        cos_theta_Wplus = angles_cuts[i, 3]

        # Find bin indices for efficiency lookup
        cos_bin_Wminus = np.digitize(cos_theta_Wminus, cos_edges) - 1
        phi_bin_Wminus = np.digitize(phi_Wminus, phi_edges) - 1
        cos_bin_Wplus = np.digitize(cos_theta_Wplus, cos_edges) - 1
        phi_bin_Wplus = np.digitize(phi_Wplus, phi_edges) - 1

        # Ensure indices are within bounds
        cos_bin_Wminus = np.clip(cos_bin_Wminus, 0, len(cos_edges) - 2)
        phi_bin_Wminus = np.clip(phi_bin_Wminus, 0, len(phi_edges) - 2)
        cos_bin_Wplus = np.clip(cos_bin_Wplus, 0, len(cos_edges) - 2)
        phi_bin_Wplus = np.clip(phi_bin_Wplus, 0, len(phi_edges) - 2)

        # Get efficiency values (inverse weighting: 1/efficiency to correct for selection bias)
        eff_Wminus = efficiency_Wminus[cos_bin_Wminus, phi_bin_Wminus]
        eff_Wplus = efficiency_Wplus[cos_bin_Wplus, phi_bin_Wplus]

        # Combined weight (avoid division by zero)
        weight = 1.0
        if eff_Wminus > 0:
            weight *= 1.0 / eff_Wminus
        if eff_Wplus > 0:
            weight *= 1.0 / eff_Wplus

        weights.append(weight)

    weights = np.array(weights)

    print(
        f"Weight statistics: min={np.min(weights):.3f}, max={np.max(weights):.3f}, mean={np.mean(weights):.3f}"
    )

    # Compute weighted Gell-Mann coefficients using static methods
    pW1_weighted = np.zeros(8)
    pW2_weighted = np.zeros(8)
    cov_weighted = np.zeros((8, 8))

    # Get Gell-Mann coefficients for cuts data
    pW1_cuts = cuts_result.pW1[0]  # Shape: (n_events, 8)
    pW2_cuts = cuts_result.pW2[0]  # Shape: (n_events, 8)
    cov_cuts = cuts_result.datasets[0]  # Shape: (n_events, 8, 8)

    # Apply weights to compute weighted averages
    total_weight = np.sum(weights)
    for i in range(len(weights)):
        weight = weights[i] / total_weight
        pW1_weighted += weight * pW1_cuts[i]
        pW2_weighted += weight * pW2_cuts[i]
        cov_weighted += weight * cov_cuts[i]

    # Create weighted density matrix using static methods
    weighted_density_matrix = np.zeros((9, 9))

    # Fill covariance terms (indices 1-8, 1-8)
    for k in range(1, 9):
        for m in range(1, 9):
            weighted_density_matrix[k, m] = cov_weighted[k - 1, m - 1] / 4.0

    # Fill pW terms
    for k in range(1, 9):
        weighted_density_matrix[0, k] = pW2_weighted[k - 1] / 2.0
        weighted_density_matrix[k, 0] = pW1_weighted[k - 1] / 2.0

    # Calculate Bell inequality using static method
    bell_value_weighted = I_3.CGLMP_test(weighted_density_matrix)

    # Visualize the weighted Gell-Mann matrix
    visualize_gellmann_matrix(
        weighted_density_matrix,
        pW1_weighted,
        pW2_weighted,
        cov_weighted,
        target_path,
        "weighted",
    )

    print(f"Weighted Bell inequality value: {bell_value_weighted:.6f}")
    print(f"Weighted Gell-Mann matrix visualizations saved to {target_path}")

    return weighted_density_matrix, bell_value_weighted, weights


def visualize_gellmann_matrix(density_matrix, pW1, pW2, cov, target_path, prefix=""):
    """
    Create visualizations for the Gell-Mann matrix components.
    """
    os.makedirs(target_path, exist_ok=True)

    # 1. Visualize the full 9x9 density matrix
    fig, ax = plt.subplots(figsize=(8, 8))
    im = ax.imshow(density_matrix, cmap="RdBu_r", aspect="equal")
    ax.set_title(f"{prefix.capitalize()} Density Matrix (9×9)", fontsize=14)
    ax.set_xlabel("Column Index")
    ax.set_ylabel("Row Index")

    # Add text annotations
    for i in range(9):
        for j in range(9):
            text = ax.text(
                j,
                i,
                f"{density_matrix[i, j]:.3f}",
                ha="center",
                va="center",
                color="black",
                fontsize=8,
            )

    plt.colorbar(im, ax=ax)
    plt.tight_layout()
    plt.savefig(os.path.join(target_path, f"{prefix}_density_matrix.png"), dpi=150)
    plt.close()

    # 2. Visualize the 8x8 covariance matrix
    fig, ax = plt.subplots(figsize=(8, 8))
    im = ax.imshow(cov, cmap="RdBu_r", aspect="equal")
    ax.set_title(f"{prefix.capitalize()} Covariance Matrix (8×8)", fontsize=14)
    ax.set_xlabel("W⁺ Index j")
    ax.set_ylabel("W⁻ Index i")

    # Add text annotations
    for i in range(8):
        for j in range(8):
            text = ax.text(
                j,
                i,
                f"{cov[i, j]:.3f}",
                ha="center",
                va="center",
                color="black",
                fontsize=8,
            )

    plt.colorbar(im, ax=ax)
    plt.tight_layout()
    plt.savefig(os.path.join(target_path, f"{prefix}_covariance_matrix.png"), dpi=150)
    plt.close()

    # 3. Visualize pW1 and pW2 coefficients
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # pW1 (W⁻) coefficients
    x_indices = np.arange(1, 9)
    ax1.bar(x_indices, pW1, alpha=0.7, color="blue")
    ax1.set_title(f"{prefix.capitalize()} W⁻ Coefficients (pW1)", fontsize=12)
    ax1.set_xlabel("Gell-Mann Index")
    ax1.set_ylabel("Coefficient Value")
    ax1.grid(True, alpha=0.3)

    # Add value labels on bars
    for i, v in enumerate(pW1):
        ax1.text(
            i + 1,
            v + 0.01 * max(abs(pW1)),
            f"{v:.3f}",
            ha="center",
            va="bottom",
            fontsize=9,
        )

    # pW2 (W⁺) coefficients
    ax2.bar(x_indices, pW2, alpha=0.7, color="red")
    ax2.set_title(f"{prefix.capitalize()} W⁺ Coefficients (pW2)", fontsize=12)
    ax2.set_xlabel("Gell-Mann Index")
    ax2.set_ylabel("Coefficient Value")
    ax2.grid(True, alpha=0.3)

    # Add value labels on bars
    for i, v in enumerate(pW2):
        ax2.text(
            i + 1,
            v + 0.01 * max(abs(pW2)),
            f"{v:.3f}",
            ha="center",
            va="bottom",
            fontsize=9,
        )

    plt.tight_layout()
    plt.savefig(
        os.path.join(target_path, f"{prefix}_gellmann_coefficients.png"), dpi=150
    )
    plt.close()

    # 4. Create a summary comparison plot
    fig, ax = plt.subplots(figsize=(10, 6))
    x_indices = np.arange(1, 9)
    width = 0.35

    ax.bar(x_indices - width / 2, pW1, width, label="W⁻ (pW1)", alpha=0.7, color="blue")
    ax.bar(x_indices + width / 2, pW2, width, label="W⁺ (pW2)", alpha=0.7, color="red")

    ax.set_title(
        f"{prefix.capitalize()} Gell-Mann Coefficients Comparison", fontsize=14
    )
    ax.set_xlabel("Gell-Mann Index")
    ax.set_ylabel("Coefficient Value")
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(
        os.path.join(target_path, f"{prefix}_coefficients_comparison.png"), dpi=150
    )
    plt.close()

    print(f"Gell-Mann matrix visualizations saved with prefix '{prefix}'")

    return


# Main execution
if __name__ == "__main__":
    print("Starting angular distribution analysis with efficiency map computation...")
    print(
        f"Using 10% of data split into two halves for realistic measurement simulation"
    )
    print(f"Efficiency map data - Truth shape: {final_state_eff.shape}")
    print(f"Efficiency map data - Cuts shape: {final_state_cuts_eff.shape}")
    print(f"Measurement data - Truth shape: {final_state_meas.shape}")
    print(f"Measurement data - Cuts shape: {final_state_cuts_meas.shape}")

    # Step 1: Compute efficiency map using first half of data
    print("\n=== Step 1: Computing efficiency map ===")
    efficiency_Wminus, efficiency_Wplus, cos_edges, phi_edges = compute_efficiency_map(
        final_state_eff,
        final_state_cuts_eff,
        types_eff,
        types_cuts_eff,
        "reports/efficiency_maps/",
    )

    # Step 2: Compute weighted Gell-Mann matrix using second half of data
    print("\n=== Step 2: Computing weighted Gell-Mann matrix ===")
    weighted_density_matrix, bell_value_weighted, weights = (
        compute_weighted_gellmann_matrix(
            final_state_cuts_meas,
            types_cuts_meas,
            efficiency_Wminus,
            efficiency_Wplus,
            cos_edges,
            phi_edges,
            "reports/weighted_analysis/",
        )
    )

    # Step 3: Compute unweighted analysis for comparison
    print("\n=== Step 3: Computing unweighted analysis for comparison ===")
    unweighted_result = calculate_results_diff_analysis(
        arrays=[final_state_cuts_meas],
        labels=["Cuts_Unweighted"],
        title="Unweighted Analysis",
        types=[types_cuts_meas],
    )
    unweighted_result.initialize_datasets()
    bell_value_unweighted = unweighted_result.bell_inequality_averaged(
        "reports/comparison/"
    )

    # Also create visualization for unweighted matrix
    unweighted_cov = np.mean(unweighted_result.datasets[0], axis=0)
    unweighted_pW1 = np.mean(unweighted_result.pW1[0], axis=0)
    unweighted_pW2 = np.mean(unweighted_result.pW2[0], axis=0)

    # Create unweighted density matrix
    unweighted_density_matrix = np.zeros((9, 9))
    for k in range(1, 9):
        for m in range(1, 9):
            unweighted_density_matrix[k, m] = unweighted_cov[k - 1, m - 1] / 4.0
    for k in range(1, 9):
        unweighted_density_matrix[0, k] = unweighted_pW2[k - 1] / 2.0
        unweighted_density_matrix[k, 0] = unweighted_pW1[k - 1] / 2.0

    visualize_gellmann_matrix(
        unweighted_density_matrix,
        unweighted_pW1,
        unweighted_pW2,
        unweighted_cov,
        "reports/comparison/",
        "unweighted",
    )

    # Step 4: Compare results
    print("\n=== Step 4: Results Summary ===")
    print(f"Unweighted Bell inequality (cuts only): {bell_value_unweighted[0]:.6f}")
    print(f"Weighted Bell inequality (efficiency corrected): {bell_value_weighted:.6f}")
    print(f"Difference: {bell_value_weighted - bell_value_unweighted[0]:.6f}")

    # Save comparison results
    comparison_results = {
        "unweighted_bell": bell_value_unweighted[0],
        "weighted_bell": bell_value_weighted,
        "difference": bell_value_weighted - bell_value_unweighted[0],
        "weight_stats": {
            "min": float(np.min(weights)),
            "max": float(np.max(weights)),
            "mean": float(np.mean(weights)),
            "std": float(np.std(weights)),
        },
    }

    import json

    os.makedirs("reports/comparison/", exist_ok=True)
    with open("reports/comparison/bell_comparison.json", "w") as f:
        json.dump(comparison_results, f, indent=2)

    print("\nAnalysis complete! Results saved in reports/ directory.")
    print("Efficiency maps: reports/efficiency_maps/")
    print("Weighted analysis: reports/weighted_analysis/")
    print("Comparison: reports/comparison/")
    print("\nGell-Mann matrix visualizations created:")
    print("- Weighted: reports/weighted_analysis/")
    print("- Unweighted: reports/comparison/")
